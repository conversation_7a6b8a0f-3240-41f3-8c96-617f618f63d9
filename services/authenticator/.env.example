# Service Configuration
SERVICE_NAME=cortexa-auth-service
DEBUG=false
HOST=0.0.0.0
PORT=8001

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=cortexa_user
DB_PASSWORD=cortexa_password
DB_NAME=cortexa

# Security Configuration
SECURITY_SECRET_KEY=your-super-secret-key-change-in-production
SECURITY_ALGORITHM=HS256
SECURITY_ACCESS_TOKEN_EXPIRE_MINUTES=30
SECURITY_REFRESH_TOKEN_EXPIRE_DAYS=7
