from fastapi import APIRouter
from src.core.config import settings

router = APIRouter()


@router.get("/health")
async def health_check():
    """Health check endpoint for Docker Compose and load balancers."""
    try:
        # Test database connection
        from cortexacommon.db import get_async_session
        from sqlalchemy import text

        session_gen = get_async_session()
        session = await session_gen.__anext__()
        try:
            await session.execute(text("SELECT 1"))
            db_status = "connected"
        finally:
            await session.close()

        return {
            "status": "healthy",
            "service": settings.service_name,
            "version": "0.1.0",
            "database": db_status
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": settings.service_name,
            "version": "0.1.0",
            "database": "disconnected",
            "error": str(e)
        }
