# Cortexa Authentication Service

Authentication and user management service for the Cortexa platform.

## Features

- User registration and management
- JWT-based authentication
- Role-based access control (RBAC)
- Password hashing and validation
- Token refresh functionality
- RESTful API endpoints

## Roles

- **ADMIN**: Full system access including user management
- **MANAGER**: Call statistics and advanced features
- **OPERATOR**: Basic call data access
- **TRANSLATOR**: Translation-specific features (planned)

## API Endpoints

### Authentication
- `POST /api/v1/auth/token` - OAuth2 compatible login
- `POST /api/v1/auth/refresh` - Refresh access token
- `GET /api/v1/auth/me` - Get current user info

### Health
- `GET /health` - Health check endpoint

## Running the Service

```bash
# Install dependencies
poetry install

# Run the service
poetry run uvicorn src.main:app --host 0.0.0.0 --port 8001 --reload

# Or use the main module
poetry run python -m src.main
```

## Environment Variables

See `.env.example` for configuration options.

## Database Migrations

```bash
# Generate migration
poetry run alembic revision --autogenerate -m "Description"

# Apply migrations
poetry run alembic upgrade head
```
