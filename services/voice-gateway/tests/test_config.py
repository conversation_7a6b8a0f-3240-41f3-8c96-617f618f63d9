import os
import pytest
from unittest.mock import patch

from src.core.config import VoiceGatewaySettings


class TestVoiceGatewaySettings:
    """Test suite for VoiceGatewaySettings configuration."""

    def test_default_values(self):
        """Test that default configuration values are set correctly."""
        settings = VoiceGatewaySettings()
        
        # Service identification
        assert settings.service_name == "voice-gateway"
        assert settings.port == 8002
        
        # S2ST Pipeline defaults
        assert settings.whisper_model_size == "base.en"
        assert settings.whisper_compute_type == "int8"
        assert settings.whisper_device == "cpu"
        
        # VAD defaults
        assert settings.vad_aggressiveness == 3
        assert settings.vad_frame_duration_ms == 30
        
        # Translation defaults
        assert settings.translation_model == "Helsinki-NLP/opus-mt-en-es"
        assert settings.translation_device == "cpu"
        
        # TTS defaults
        assert settings.tts_provider == "openai"
        assert settings.tts_model == "tts-1"
        assert settings.tts_voice == "alloy"
        assert settings.tts_api_key is None
        
        # Audio processing defaults
        assert settings.audio_sample_rate == 16000
        
        # WebSocket defaults
        assert settings.ws_max_connections == 100
        assert settings.ws_heartbeat_interval == 30
        assert settings.ws_connection_timeout == 300
        
        # External services defaults
        assert settings.call_data_service_url == "http://localhost:8003"

    def test_environment_variable_override(self):
        """Test that environment variables override default values."""
        env_vars = {
            "SERVICE_NAME": "test-voice-gateway",
            "PORT": "9999",
            "WHISPER_MODEL_SIZE": "large",
            "VAD_AGGRESSIVENESS": "1",
            "TTS_API_KEY": "test-api-key-123",
            "AUDIO_SAMPLE_RATE": "48000",
            "WS_MAX_CONNECTIONS": "50",
            "CALL_DATA_SERVICE_URL": "http://test-service:8003",
        }
        
        with patch.dict(os.environ, env_vars):
            settings = VoiceGatewaySettings()
            
            assert settings.service_name == "test-voice-gateway"
            assert settings.port == 9999
            assert settings.whisper_model_size == "large"
            assert settings.vad_aggressiveness == 1
            assert settings.tts_api_key == "test-api-key-123"
            assert settings.audio_sample_rate == 48000
            assert settings.ws_max_connections == 50
            assert settings.call_data_service_url == "http://test-service:8003"

    def test_vad_aggressiveness_validation(self):
        """Test VAD aggressiveness level validation."""
        # Valid values (0-3)
        for level in [0, 1, 2, 3]:
            settings = VoiceGatewaySettings(vad_aggressiveness=level)
            assert settings.vad_aggressiveness == level
        
        # Invalid values should raise validation error
        with pytest.raises(ValueError):
            VoiceGatewaySettings(vad_aggressiveness=-1)
        
        with pytest.raises(ValueError):
            VoiceGatewaySettings(vad_aggressiveness=4)

    def test_whisper_model_size_options(self):
        """Test different Whisper model size configurations."""
        valid_models = ["tiny", "tiny.en", "base", "base.en", "small", "small.en", 
                       "medium", "medium.en", "large", "large-v1", "large-v2", "large-v3"]
        
        for model in valid_models:
            settings = VoiceGatewaySettings(whisper_model_size=model)
            assert settings.whisper_model_size == model

    def test_whisper_compute_type_options(self):
        """Test different Whisper compute type configurations."""
        valid_types = ["int8", "int16", "float16", "float32"]
        
        for compute_type in valid_types:
            settings = VoiceGatewaySettings(whisper_compute_type=compute_type)
            assert settings.whisper_compute_type == compute_type

    def test_device_options(self):
        """Test device configuration options."""
        valid_devices = ["cpu", "cuda", "cuda:0", "cuda:1"]
        
        for device in valid_devices:
            settings = VoiceGatewaySettings(whisper_device=device, translation_device=device)
            assert settings.whisper_device == device
            assert settings.translation_device == device

    def test_tts_provider_options(self):
        """Test different TTS provider configurations."""
        valid_providers = ["openai", "google", "azure", "aws"]
        
        for provider in valid_providers:
            settings = VoiceGatewaySettings(tts_provider=provider)
            assert settings.tts_provider == provider

    def test_audio_configuration(self):
        """Test audio processing configuration."""
        settings = VoiceGatewaySettings(
            audio_sample_rate=48000,
        )

        assert settings.audio_sample_rate == 48000

    def test_websocket_configuration(self):
        """Test WebSocket configuration parameters."""
        settings = VoiceGatewaySettings(
            ws_max_connections=200,
            ws_heartbeat_interval=60,
            ws_connection_timeout=600
        )
        
        assert settings.ws_max_connections == 200
        assert settings.ws_heartbeat_interval == 60
        assert settings.ws_connection_timeout == 600

    def test_external_service_urls(self):
        """Test external service URL configuration."""
        settings = VoiceGatewaySettings(
            call_data_service_url="https://call-data.example.com",
        )

        assert settings.call_data_service_url == "https://call-data.example.com"

    def test_case_insensitive_env_vars(self):
        """Test that environment variables are case insensitive."""
        env_vars = {
            "service_name": "lowercase-service",
            "PORT": "8888",
            "Whisper_Model_Size": "mixed-case-model",
        }
        
        with patch.dict(os.environ, env_vars):
            settings = VoiceGatewaySettings()
            
            assert settings.service_name == "lowercase-service"
            assert settings.port == 8888
            # Note: This test depends on the actual case sensitivity behavior
            # of pydantic-settings, which may vary

    def test_extra_fields_ignored(self):
        """Test that extra configuration fields are ignored."""
        # This should not raise an error due to extra="ignore"
        settings = VoiceGatewaySettings(
            unknown_field="should_be_ignored",
            another_unknown="also_ignored"
        )
        
        # The unknown fields should not be present
        assert not hasattr(settings, "unknown_field")
        assert not hasattr(settings, "another_unknown")

    def test_field_descriptions(self):
        """Test that configuration fields have proper descriptions."""
        settings = VoiceGatewaySettings()

        # Check that field info contains descriptions
        fields = VoiceGatewaySettings.model_fields

        assert fields["service_name"].description == "Service name"
        assert fields["whisper_model_size"].description is not None
        assert fields["vad_aggressiveness"].description is not None

    def test_settings_serialization(self):
        """Test that settings can be serialized to dict."""
        settings = VoiceGatewaySettings(
            service_name="test-service",
            port=8080,
            tts_api_key="secret-key"
        )
        
        settings_dict = settings.model_dump()
        
        assert isinstance(settings_dict, dict)
        assert settings_dict["service_name"] == "test-service"
        assert settings_dict["port"] == 8080
        assert settings_dict["tts_api_key"] == "secret-key"

    def test_settings_from_dict(self):
        """Test creating settings from dictionary."""
        config_dict = {
            "service_name": "dict-service",
            "port": 7777,
            "whisper_model_size": "small",
            "vad_aggressiveness": 2,
        }
        
        settings = VoiceGatewaySettings(**config_dict)
        
        assert settings.service_name == "dict-service"
        assert settings.port == 7777
        assert settings.whisper_model_size == "small"
        assert settings.vad_aggressiveness == 2
