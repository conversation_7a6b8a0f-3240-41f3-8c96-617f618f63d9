"""
Service locator for shared application components.

This module provides a service locator pattern for accessing shared components
in contexts where dependency injection is not available (like background tasks).

This is a cleaner alternative to direct global variables and provides better
testability and lifecycle management.
"""
from typing import Optional, TYPE_CHECKING
from contextlib import contextmanager

if TYPE_CHECKING:
    from src.pipeline.s2st import S2STProcessor


class ServiceLocator:
    """Service locator for application-wide components."""

    def __init__(self):
        self._s2st_processor: Optional["S2STProcessor"] = None

    def set_s2st_processor(self, processor: "S2STProcessor") -> None:
        """Register the S2ST processor instance."""
        self._s2st_processor = processor

    def get_s2st_processor(self) -> Optional["S2STProcessor"]:
        """Get the S2ST processor instance."""
        return self._s2st_processor

    def clear(self) -> None:
        """Clear all registered services."""
        self._s2st_processor = None

    @contextmanager
    def processor_context(self, processor: "S2STProcessor"):
        """Context manager for temporarily setting a processor (useful for testing)."""
        old_processor = self._s2st_processor
        self._s2st_processor = processor
        try:
            yield
        finally:
            self._s2st_processor = old_processor


# Global service locator instance
_service_locator = ServiceLocator()


def get_service_locator() -> ServiceLocator:
    """Get the global service locator instance."""
    return _service_locator


# Convenience functions for backward compatibility
def set_s2st_processor(processor: "S2STProcessor") -> None:
    """Set the global S2ST processor instance."""
    _service_locator.set_s2st_processor(processor)


def get_s2st_processor() -> Optional["S2STProcessor"]:
    """Get the global S2ST processor instance."""
    return _service_locator.get_s2st_processor()


def clear_s2st_processor() -> None:
    """Clear the global S2ST processor instance."""
    _service_locator.clear()
